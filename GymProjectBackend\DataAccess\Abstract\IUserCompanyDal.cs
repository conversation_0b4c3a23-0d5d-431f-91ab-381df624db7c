﻿using Core.DataAccess;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IUserCompanyDal:IEntityRepository<UserCompany>
    {
        List<UserCompanyDetailDto> GetUserCompanyDetails();
        int GetUserCompanyId(int userId);
        List<UserCompany> GetActiveUserCompanies(int userId);
    }
}
