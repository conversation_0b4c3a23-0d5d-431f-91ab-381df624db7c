using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Business;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.DTOs;
using System.Collections.Generic;
using System.Linq;

namespace Business.Concrete
{
    public class WorkoutProgramTemplateManager : IWorkoutProgramTemplateService
    {
        private readonly IWorkoutProgramTemplateDal _workoutProgramTemplateDal;
        private readonly ICompanyContext _companyContext;

        public WorkoutProgramTemplateManager(
            IWorkoutProgramTemplateDal workoutProgramTemplateDal,
            ICompanyContext companyContext)
        {
            _workoutProgramTemplateDal = workoutProgramTemplateDal;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 30, "WorkoutProgramTemplate", "List")]
        [PerformanceAspect(3)]
        public IDataResult<List<WorkoutProgramTemplateListDto>> GetAll()
        {
            var result = _workoutProgramTemplateDal.GetWorkoutProgramTemplateList();
            return new SuccessDataResult<List<WorkoutProgramTemplateListDto>>(result, Messages.WorkoutProgramsListed);
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 30, "WorkoutProgramTemplate", "Detail")]
        [PerformanceAspect(3)]
        public IDataResult<WorkoutProgramTemplateDto> GetById(int templateId)
        {
            // Önce template'ın bu company'ye ait olup olmadığını kontrol et
            var template = _workoutProgramTemplateDal.Get(t => t.WorkoutProgramTemplateID == templateId && t.CompanyID == _companyContext.GetCompanyId());
            if (template == null)
            {
                return new ErrorDataResult<WorkoutProgramTemplateDto>("Antrenman programı bulunamadı veya erişim yetkiniz yok.");
            }

            var result = _workoutProgramTemplateDal.GetWorkoutProgramTemplateDetail(templateId);
            if (result == null)
            {
                return new ErrorDataResult<WorkoutProgramTemplateDto>(Messages.WorkoutProgramNotFound);
            }
            return new SuccessDataResult<WorkoutProgramTemplateDto>(result, Messages.WorkoutProgramDetailRetrieved);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(WorkoutProgramTemplateAddValidator))]
        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("WorkoutProgramTemplate")]
        [PerformanceAspect(3)]
        public IResult Add(WorkoutProgramTemplateAddDto templateAddDto)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfProgramNameExists(templateAddDto.ProgramName),
                CheckMaxDayCount(templateAddDto.Days.Count),
                CheckDayNumbers(templateAddDto.Days),
                CheckAtLeastOneWorkoutDay(templateAddDto.Days)
            );

            if (ruleResult != null)
            {
                return ruleResult;
            }

            var companyId = _companyContext.GetCompanyId();

            // Karmaşık işlemi DAL'a devret
            _workoutProgramTemplateDal.AddWorkoutProgramWithDaysAndExercises(templateAddDto, companyId);

            return new SuccessResult(Messages.WorkoutProgramAdded);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(WorkoutProgramTemplateUpdateValidator))]
        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("WorkoutProgramTemplate")]
        [PerformanceAspect(3)]
        public IResult Update(WorkoutProgramTemplateUpdateDto templateUpdateDto)
        {
            // Mevcut programı kontrol et
            var existingTemplate = _workoutProgramTemplateDal.Get(t => t.WorkoutProgramTemplateID == templateUpdateDto.WorkoutProgramTemplateID);
            if (existingTemplate == null)
            {
                return new ErrorResult(Messages.WorkoutProgramNotFound);
            }

            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfProgramNameExistsForUpdate(templateUpdateDto.ProgramName, templateUpdateDto.WorkoutProgramTemplateID),
                CheckMaxDayCount(templateUpdateDto.Days.Count),
                CheckDayNumbers(templateUpdateDto.Days),
                CheckAtLeastOneWorkoutDay(templateUpdateDto.Days)
            );

            if (ruleResult != null)
            {
                return ruleResult;
            }

            var companyId = _companyContext.GetCompanyId();

            // Karmaşık işlemi DAL'a devret
            _workoutProgramTemplateDal.UpdateWorkoutProgramWithDaysAndExercises(templateUpdateDto, companyId);

            return new SuccessResult(Messages.WorkoutProgramUpdated);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [SmartCacheRemoveAspect("WorkoutProgramTemplate")]
        [PerformanceAspect(3)]
        public IResult Delete(int templateId)
        {
            var template = _workoutProgramTemplateDal.Get(t => t.WorkoutProgramTemplateID == templateId);
            if (template == null)
            {
                return new ErrorResult(Messages.WorkoutProgramNotFound);
            }

            _workoutProgramTemplateDal.Delete(templateId);
            return new SuccessResult(Messages.WorkoutProgramDeleted);
        }

        // İş Kuralları
        private IResult CheckIfProgramNameExists(string programName)
        {
            bool exists = _workoutProgramTemplateDal.CheckProgramNameExists(programName);
            if (exists)
            {
                return new ErrorResult(Messages.WorkoutProgramNameExists);
            }
            return new SuccessResult();
        }

        private IResult CheckIfProgramNameExistsForUpdate(string programName, int templateId)
        {
            bool exists = _workoutProgramTemplateDal.CheckProgramNameExists(programName, templateId);
            if (exists)
            {
                return new ErrorResult(Messages.WorkoutProgramNameExists);
            }
            return new SuccessResult();
        }

        private IResult CheckMaxDayCount(int dayCount)
        {
            if (dayCount != 7)
            {
                return new ErrorResult("Antrenman programı tam olarak 7 gün olmalıdır.");
            }
            return new SuccessResult();
        }

        private IResult CheckDayNumbers(List<WorkoutProgramDayAddDto> days)
        {
            var dayNumbers = days.Select(d => d.DayNumber).ToList();

            // Gün numaraları benzersiz olmalı
            if (dayNumbers.Count != dayNumbers.Distinct().Count())
            {
                return new ErrorResult(Messages.DayNumbersMustBeUnique);
            }

            // Gün numaraları 1-7 arasında olmalı
            if (dayNumbers.Any(d => d < 1 || d > 7))
            {
                return new ErrorResult(Messages.DayNumbersInvalidRange);
            }

            return new SuccessResult();
        }

        private IResult CheckAtLeastOneWorkoutDay(List<WorkoutProgramDayAddDto> days)
        {
            // En az bir gün egzersiz günü olmalı (IsRestDay = false)
            if (days.All(d => d.IsRestDay))
            {
                return new ErrorResult(Messages.AtLeastOneWorkoutDayRequired);
            }

            return new SuccessResult();
        }

        private IResult CheckDayNumbers(List<WorkoutProgramDayUpdateDto> days)
        {
            var dayNumbers = days.Select(d => d.DayNumber).ToList();

            // Gün numaraları benzersiz olmalı
            if (dayNumbers.Count != dayNumbers.Distinct().Count())
            {
                return new ErrorResult(Messages.DayNumbersMustBeUnique);
            }

            // Gün numaraları 1-7 arasında olmalı
            if (dayNumbers.Any(d => d < 1 || d > 7))
            {
                return new ErrorResult(Messages.DayNumbersInvalidRange);
            }

            return new SuccessResult();
        }

        private IResult CheckAtLeastOneWorkoutDay(List<WorkoutProgramDayUpdateDto> days)
        {
            // En az bir gün egzersiz günü olmalı (IsRestDay = false)
            if (days.All(d => d.IsRestDay))
            {
                return new ErrorResult(Messages.AtLeastOneWorkoutDayRequired);
            }

            return new SuccessResult();
        }
    }
}
